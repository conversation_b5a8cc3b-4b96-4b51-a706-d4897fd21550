import React from 'react';

interface AnimatedBackgroundProps {
  className?: string;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({ className = "" }) => {
  return (
    <div className={`relative w-full h-full overflow-hidden ${className}`}>
      {/* الطبقة الأساسية */}
      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e, #0f0f23)',
          backgroundSize: '400% 400%',
          animation: 'gradientShift 15s ease infinite'
        }}
      />

      {/* الطبقة الأولى - دوائر متحركة */}
      <div
        className="absolute inset-0"
        style={{
          background: `
            radial-gradient(circle at 20% 50%, rgba(192, 132, 252, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 40% 80%, rgba(168, 85, 247, 0.2) 0%, transparent 50%)
          `,
          animation: 'float1 20s ease-in-out infinite'
        }}
      />

      {/* الطبقة الثانية - دوائر متحركة عكسية */}
      <div
        className="absolute inset-0"
        style={{
          background: `
            radial-gradient(circle at 60% 30%, rgba(192, 132, 252, 0.1) 0%, transparent 40%),
            radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.1) 0%, transparent 40%),
            radial-gradient(circle at 70% 60%, rgba(168, 85, 247, 0.1) 0%, transparent 40%)
          `,
          animation: 'float2 25s ease-in-out infinite reverse'
        }}
      />

      {/* الطبقة الثالثة - موجات */}
      <div
        className="absolute inset-0 opacity-60"
        style={{
          background: `
            linear-gradient(45deg, transparent 30%, rgba(192, 132, 252, 0.05) 50%, transparent 70%),
            linear-gradient(-45deg, transparent 30%, rgba(139, 92, 246, 0.05) 50%, transparent 70%)
          `,
          animation: 'wave 30s linear infinite'
        }}
      />

      {/* طبقة الجسيمات */}
      <div
        className="absolute inset-0 opacity-80"
        style={{
          backgroundImage: `
            radial-gradient(2px 2px at 20px 30px, rgba(192, 132, 252, 0.4), transparent),
            radial-gradient(2px 2px at 40px 70px, rgba(139, 92, 246, 0.4), transparent),
            radial-gradient(1px 1px at 90px 40px, rgba(168, 85, 247, 0.4), transparent),
            radial-gradient(1px 1px at 130px 80px, rgba(192, 132, 252, 0.4), transparent),
            radial-gradient(2px 2px at 160px 30px, rgba(139, 92, 246, 0.4), transparent)
          `,
          backgroundRepeat: 'repeat',
          backgroundSize: '200px 100px',
          animation: 'sparkle 20s linear infinite'
        }}
      />
    </div>
  );
};

export default AnimatedBackground;
