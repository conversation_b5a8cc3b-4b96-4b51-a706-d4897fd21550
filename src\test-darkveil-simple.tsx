import DarkVeil from '@/components/ui/DarkVeil';

export default function TestDarkVeilSimple() {
  return (
    <div style={{ width: '100vw', height: '100vh', background: '#000' }}>
      <h1 style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', color: 'white', zIndex: 10 }}>
        DarkVeil Test
      </h1>
      <DarkVeil 
        hueShift={270}
        noiseIntensity={0.1}
        scanlineIntensity={0.1}
        speed={0.5}
        scanlineFrequency={2}
        warpAmount={0.3}
        resolutionScale={1}
      />
    </div>
  );
}
